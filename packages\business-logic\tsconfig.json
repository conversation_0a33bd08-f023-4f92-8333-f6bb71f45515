{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES2017"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "composite": true, "declaration": true, "declarationMap": true, "outDir": "./dist"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}