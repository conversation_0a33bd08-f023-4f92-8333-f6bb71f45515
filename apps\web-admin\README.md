# Tap2Go Web Admin App

A clean, simple Next.js application for the Tap2Go admin portal.

## 🚀 Features

- **Clean Setup**: Fresh Next.js 15 app with React 19
- **Tailwind CSS**: Modern styling with Tailwind CSS v4
- **TypeScript**: Full TypeScript support
- **Vercel Ready**: Optimized for Vercel deployment

## 🛠️ Development

Run the development server on port 3003:

```bash
pnpm dev
```

Open [http://localhost:3003](http://localhost:3003) with your browser to see the result.

## 📦 Build

Build for production:

```bash
pnpm build
```

## 🚀 Deploy to Vercel

This app is configured for easy Vercel deployment with:
- Turborepo monorepo support
- Node.js 22 runtime
- Optimized build commands

Simply connect your repository to Vercel and deploy!
