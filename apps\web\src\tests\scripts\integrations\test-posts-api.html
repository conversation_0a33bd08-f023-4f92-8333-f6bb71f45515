<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Posts API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .loading {
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Posts API Test Tool</h1>
        <p>Test your blog posts API endpoints to debug production issues.</p>
        
        <div>
            <button onclick="testDebugEndpoint()">🔍 Test Debug Endpoint</button>
            <button onclick="testMainEndpoint()">📝 Test Main Posts Endpoint</button>
            <button onclick="testPublishedPosts()">✅ Test Published Posts</button>
            <button onclick="testDraftPosts()">📄 Test Draft Posts</button>
            <button onclick="testAllEndpoints()">🚀 Test All Endpoints</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const baseUrl = window.location.origin;
        
        function addResult(title, data, isError = false) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'container';
            
            const titleEl = document.createElement('h3');
            titleEl.textContent = title;
            resultDiv.appendChild(titleEl);
            
            const contentDiv = document.createElement('div');
            contentDiv.className = `result ${isError ? 'error' : 'success'}`;
            contentDiv.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            resultDiv.appendChild(contentDiv);
            
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        function addLoading(title) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'container';
            resultDiv.id = 'loading-' + Date.now();
            
            const titleEl = document.createElement('h3');
            titleEl.textContent = title;
            resultDiv.appendChild(titleEl);
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'result loading';
            contentDiv.textContent = 'Loading...';
            resultDiv.appendChild(contentDiv);
            
            resultsDiv.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
            
            return resultDiv;
        }
        
        async function makeRequest(endpoint, title) {
            const loadingDiv = addLoading(title);
            
            try {
                const response = await fetch(`${baseUrl}${endpoint}`);
                const data = await response.json();
                
                loadingDiv.remove();
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    ok: response.ok,
                    data: data
                };
                
                addResult(title, result, !response.ok);
                return result;
            } catch (error) {
                loadingDiv.remove();
                addResult(title, `Error: ${error.message}`, true);
                return null;
            }
        }
        
        async function testDebugEndpoint() {
            await makeRequest('/api/debug/posts', '🔍 Debug Endpoint Results');
        }
        
        async function testMainEndpoint() {
            await makeRequest('/api/blog/posts', '📝 Main Posts Endpoint Results');
        }
        
        async function testPublishedPosts() {
            await makeRequest('/api/blog/posts?status=published', '✅ Published Posts Results');
        }
        
        async function testDraftPosts() {
            await makeRequest('/api/blog/posts?status=draft', '📄 Draft Posts Results');
        }
        
        async function testAllEndpoints() {
            document.getElementById('results').innerHTML = '';
            
            await testDebugEndpoint();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testMainEndpoint();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testPublishedPosts();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDraftPosts();
            
            addResult('🎉 All Tests Completed', 'Check the results above for any issues.');
        }
        
        // Auto-run debug test on page load
        window.addEventListener('load', () => {
            setTimeout(testDebugEndpoint, 1000);
        });
    </script>
</body>
</html>
