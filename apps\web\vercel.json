{"version": 2, "framework": "nextjs", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "env": {"NODE_VERSION": "20", "NEXT_TELEMETRY_DISABLED": "1"}, "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1"}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}