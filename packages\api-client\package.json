{"name": "api-client", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@reduxjs/toolkit": "^2.5.0", "axios": "^1.9.0", "shared-types": "workspace:*", "shared-utils": "workspace:*", "typescript": "^5.8.3"}, "devDependencies": {"@types/node": "^20.17.57"}}