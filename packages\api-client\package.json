{"name": "api-client", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "npx tsc", "dev": "npx tsc --watch", "lint": "eslint src/", "type-check": "npx tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@reduxjs/toolkit": "^2.5.0", "axios": "^1.9.0", "shared-types": "workspace:*", "shared-utils": "workspace:*"}, "devDependencies": {"@types/node": "^20.17.57", "typescript": "^5.8.3"}}