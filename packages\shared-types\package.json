{"name": "shared-types", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "peerDependencies": {"react": "^19.0.0", "@types/react": "~19.0.10"}, "dependencies": {"react": "^19.0.0", "@types/react": "~19.0.10", "typescript": "^5.8.3"}, "devDependencies": {"rimraf": "^6.0.1"}}