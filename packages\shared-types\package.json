{"name": "shared-types", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"build": "pnpm exec tsc", "dev": "pnpm exec tsc --watch", "lint": "eslint src/", "type-check": "pnpm exec tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "peerDependencies": {"react": "^19.0.0", "@types/react": "~19.0.10"}, "devDependencies": {"react": "^19.0.0", "@types/react": "~19.0.10", "rimraf": "^6.0.1", "typescript": "^5.8.3"}}