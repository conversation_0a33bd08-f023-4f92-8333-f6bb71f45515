{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "pnpm run build && firebase emulators:start --only functions", "shell": "pnpm run build && firebase functions:shell", "start": "pnpm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "lint": "eslint src --ext .ts"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1"}, "devDependencies": {"@types/node": "^22.0.0", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.15.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0"}, "private": true}