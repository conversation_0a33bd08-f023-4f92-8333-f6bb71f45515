rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(uid) {
      return request.auth.uid == uid;
    }

    function getUserRole() {
      // Safe role check - return null if user doc doesn't exist
      return exists(/databases/$(database)/documents/users/$(request.auth.uid)) ?
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role : null;
    }

    function isAdmin() {
      return isAuthenticated() && getUserRole() == 'admin';
    }

    function isVendor() {
      return isAuthenticated() && getUserRole() == 'vendor';
    }

    function isCustomer() {
      return isAuthenticated() && getUserRole() == 'customer';
    }

    function isDriver() {
      return isAuthenticated() && getUserRole() == 'driver';
    }

    function isActiveUser() {
      return exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isActive == true;
    }

    // ===== USERS COLLECTION =====
    match /users/{userId} {
      // Users can read their own document, admins can read all
      allow read: if isOwner(userId) || isAdmin();

      // Users can create their own document during signup
      // Allow admin role creation for initial admin setup
      allow create: if isOwner(userId) &&
                       request.resource.data.uid == userId &&
                       (request.resource.data.email == request.auth.token.email ||
                        (request.resource.data.role == 'admin' &&
                         request.resource.data.email in ['<EMAIL>', '<EMAIL>']));

      // Users can update their own document (except role), admins can update any
      // Allow role updates for initial admin setup
      allow update: if (isOwner(userId) &&
                       (!('role' in request.resource.data.diff(resource.data).affectedKeys()) ||
                        (request.resource.data.role == 'admin' &&
                         request.resource.data.email in ['<EMAIL>', '<EMAIL>']))) ||
                       isAdmin();

      // Only admins can delete users
      allow delete: if isAdmin();
    }

    // ===== ADMINS COLLECTION =====
    match /admins/{adminId} {
      // Only admins can read admin documents
      allow read: if isAdmin();

      // Allow admin creation for:
      // 1. Super admins creating new admins
      // 2. Initial admin creation (when no admin documents exist yet)
      allow create: if (isAdmin() &&
                       get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.accessLevel == 'super_admin') ||
                       (isOwner(adminId) &&
                        request.resource.data.accessLevel == 'super_admin' &&
                        !exists(/databases/$(database)/documents/admins/$(request.auth.uid)));

      // Admins can update their own document, super admins can update any
      allow update: if (isAdmin() && isOwner(adminId)) ||
                       (isAdmin() &&
                        get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.accessLevel == 'super_admin');

      // Only super admins can delete admin documents
      allow delete: if isAdmin() &&
                       get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.accessLevel == 'super_admin';

      // Admin Actions subcollection
      match /adminActions/{actionId} {
        allow read, write: if isAdmin() && isOwner(adminId);
      }
    }

    // ===== VENDORS COLLECTION =====
    match /vendors/{vendorId} {
      // Vendors can read their own document, admins can read all, customers can read approved vendors
      allow read: if isOwner(vendorId) ||
                     isAdmin() ||
                     (isCustomer() && resource.data.status == 'approved');

      // Only vendors can create their own document
      allow create: if isVendor() && isOwner(vendorId) && isActiveUser();

      // Vendors can update their own document (except status), admins can update any
      allow update: if (isVendor() && isOwner(vendorId) && isActiveUser() &&
                       !('status' in request.resource.data.diff(resource.data).affectedKeys())) ||
                       isAdmin();

      // Only admins can delete vendor documents
      allow delete: if isAdmin();

      // Vendor Documents subcollection
      match /vendorDocuments/{documentId} {
        allow read: if isOwner(vendorId) || isAdmin();
        allow create: if isVendor() && isOwner(vendorId) && isActiveUser();
        allow update: if isAdmin(); // Only admins can approve/reject documents
        allow delete: if isOwner(vendorId) || isAdmin();
      }

      // Vendor Analytics subcollection
      match /vendorAnalytics/{analyticsId} {
        allow read: if isOwner(vendorId) || isAdmin();
        allow write: if isAdmin(); // Only system/admins can write analytics
      }

      // Vendor Payouts subcollection
      match /vendorPayouts/{payoutId} {
        allow read: if isOwner(vendorId) || isAdmin();
        allow create: if isAdmin(); // Only admins can create payouts
        allow update: if isAdmin(); // Only admins can update payout status
      }
    }

    // ===== DRIVERS COLLECTION =====
    match /drivers/{driverId} {
      // Drivers can read their own document, admins can read all
      allow read: if isOwner(driverId) || isAdmin();

      // Only drivers can create their own document
      allow create: if isDriver() && isOwner(driverId) && isActiveUser() &&
                       request.resource.data.uid == driverId;

      // Drivers can update their own document (except status), admins can update any
      allow update: if (isDriver() && isOwner(driverId) && isActiveUser() &&
                       !('status' in request.resource.data.diff(resource.data).affectedKeys())) ||
                       isAdmin();

      // Only admins can delete driver documents
      allow delete: if isAdmin();

      // Driver Documents subcollection
      match /driverDocuments/{documentId} {
        allow read: if isOwner(driverId) || isAdmin();
        allow create: if isDriver() && isOwner(driverId) && isActiveUser();
        allow update: if isAdmin(); // Only admins can approve/reject documents
        allow delete: if isOwner(driverId) || isAdmin();
      }

      // Driver Analytics subcollection
      match /driverAnalytics/{analyticsId} {
        allow read: if isOwner(driverId) || isAdmin();
        allow write: if isAdmin(); // Only system/admins can write analytics
      }

      // Driver Earnings subcollection
      match /driverEarnings/{earningsId} {
        allow read: if isOwner(driverId) || isAdmin();
        allow create: if isAdmin(); // Only admins can create earnings records
        allow update: if isAdmin(); // Only admins can update earnings
      }
    }

    // ===== CUSTOMERS COLLECTION =====
    match /customers/{customerId} {
      // Customers can read their own document, admins can read all
      allow read: if isOwner(customerId) || isAdmin();

      // Only customers can create their own document
      allow create: if isCustomer() && isOwner(customerId) && isActiveUser();

      // Customers can update their own document, admins can update any
      allow update: if (isCustomer() && isOwner(customerId) && isActiveUser()) || isAdmin();

      // Only admins can delete customer documents
      allow delete: if isAdmin();

      // Customer Addresses subcollection
      match /customerAddresses/{addressId} {
        allow read, write: if isCustomer() && isOwner(customerId) && isActiveUser();
        allow read: if isAdmin();
      }

      // Customer Payment Methods subcollection
      match /customerPaymentMethods/{paymentMethodId} {
        allow read, write: if isCustomer() && isOwner(customerId) && isActiveUser();
        allow read: if isAdmin();
      }

      // Customer Preferences subcollection
      match /customerPreferences/{preferencesId} {
        allow read, write: if isCustomer() && isOwner(customerId) && isActiveUser();
      }
    }

    // ===== RESTAURANTS COLLECTION =====
    match /restaurants/{restaurantId} {
      // Everyone can read restaurants (public access for food delivery app)
      // Owners and admins can read all, others can read approved restaurants
      allow read: if true;

      // Only vendors can create restaurants
      allow create: if isVendor() && isActiveUser() &&
                       request.resource.data.ownerId == request.auth.uid;

      // Restaurant owners can update their own restaurants (except status), admins can update any
      allow update: if (isVendor() && isOwner(resource.data.ownerId) && isActiveUser() &&
                       !('status' in request.resource.data.diff(resource.data).affectedKeys())) ||
                       isAdmin();

      // Only admins can delete restaurants
      allow delete: if isAdmin();

      // Menu Items subcollection
      match /menuItems/{itemId} {
        allow read: if true; // Everyone can read menu items
        allow write: if isVendor() && isOwner(resource.data.restaurantOwnerId) && isActiveUser();
      }
    }

    // ===== ORDERS COLLECTION =====
    match /orders/{orderId} {
      // Customers can read their own orders, vendors can read orders for their restaurants,
      // drivers can read orders assigned to them, admins can read all
      allow read: if isOwner(resource.data.customerId) ||
                     (isVendor() && resource.data.restaurantOwnerId == request.auth.uid) ||
                     (isDriver() && resource.data.driverId == request.auth.uid) ||
                     isAdmin();

      // Only customers can create orders
      allow create: if isCustomer() && isActiveUser() &&
                       request.resource.data.customerId == request.auth.uid;

      // Customers can update their orders (limited fields), vendors can update order status,
      // drivers can update delivery status, admins can update any
      allow update: if (isCustomer() && isOwner(resource.data.customerId) && isActiveUser() &&
                       request.resource.data.status in ['pending', 'cancelled']) ||
                       (isVendor() && resource.data.restaurantOwnerId == request.auth.uid && isActiveUser() &&
                       request.resource.data.status in ['confirmed', 'preparing', 'ready']) ||
                       (isDriver() && resource.data.driverId == request.auth.uid &&
                       request.resource.data.status in ['picked_up', 'delivered']) ||
                       isAdmin();

      // Only admins can delete orders
      allow delete: if isAdmin();

      // Order Items subcollection
      match /orderItems/{itemId} {
        allow read: if isOwner(resource.data.customerId) ||
                       (isVendor() && resource.data.restaurantOwnerId == request.auth.uid) ||
                       (isDriver() && resource.data.driverId == request.auth.uid) ||
                       isAdmin();
        allow write: if isCustomer() && isOwner(resource.data.customerId) && isActiveUser();
      }

      // Order Tracking subcollection
      match /orderTracking/{trackingId} {
        allow read: if isOwner(resource.data.customerId) ||
                       (isVendor() && resource.data.restaurantOwnerId == request.auth.uid) ||
                       (isDriver() && resource.data.driverId == request.auth.uid) ||
                       isAdmin();
        allow write: if isAdmin() ||
                        (isDriver() && resource.data.driverId == request.auth.uid);
      }
    }

    // ===== CATEGORIES COLLECTION =====
    match /categories/{categoryId} {
      // Everyone can read categories (public access for food delivery app)
      allow read: if true;
      // Only admins can write categories
      allow write: if isAdmin();
    }

    // ===== REVIEWS COLLECTION =====
    match /reviews/{reviewId} {
      // Everyone can read reviews (public access for food delivery app)
      allow read: if true;

      // Only customers who have ordered from the restaurant can create reviews
      allow create: if isCustomer() && isActiveUser() &&
                       request.resource.data.customerId == request.auth.uid &&
                       exists(/databases/$(database)/documents/orders/$(request.resource.data.orderId)) &&
                       get(/databases/$(database)/documents/orders/$(request.resource.data.orderId)).data.customerId == request.auth.uid;

      // Customers can update their own reviews, admins can update any
      allow update: if (isCustomer() && isOwner(resource.data.customerId) && isActiveUser()) ||
                       isAdmin();

      // Only admins can delete reviews
      allow delete: if isAdmin();
    }

    // ===== FCM TOKENS COLLECTION =====
    match /fcmTokens/{tokenId} {
      // Users can manage their own FCM tokens
      allow read, write: if isAuthenticated() &&
                            (resource.data.userId == request.auth.uid ||
                             request.resource.data.userId == request.auth.uid);
      // Admins can read all tokens for system management
      allow read: if isAdmin();
    }

    // ===== NOTIFICATIONS COLLECTION =====
    match /notifications/{notificationId} {
      // Users can read their own notifications
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
      // Users can update their own notifications (mark as read)
      allow update: if isAuthenticated() && resource.data.userId == request.auth.uid;
      // Only admins can create and delete notifications
      allow create, delete: if isAdmin();
    }

    // ===== MENU ITEMS COLLECTION (Standalone) =====
    match /menuItems/{itemId} {
      // Everyone can read menu items (public access for food delivery app)
      allow read: if true;
      // Only restaurant owners can create/update their menu items
      allow create: if isVendor() && isActiveUser() &&
                       request.resource.data.restaurantOwnerId == request.auth.uid;
      allow update: if isVendor() && isActiveUser() &&
                       resource.data.restaurantOwnerId == request.auth.uid;
      // Only admins and restaurant owners can delete menu items
      allow delete: if isAdmin() ||
                       (isVendor() && isActiveUser() && resource.data.restaurantOwnerId == request.auth.uid);
    }

    // ===== ANALYTICS COLLECTION =====
    match /analytics/{analyticsId} {
      // Only admins can read analytics
      allow read: if isAdmin();
      // Only system/admins can write analytics
      allow write: if isAdmin();
    }

    // ===== PLATFORM CONFIG COLLECTION =====
    match /platformConfig/{configId} {
      // Everyone can read platform configuration (app settings, etc.)
      allow read: if true;
      // Only admins can write platform configuration
      allow write: if isAdmin();
    }

    // ===== DISPUTES COLLECTION =====
    match /disputes/{disputeId} {
      // Users can read disputes they're involved in, admins can read all
      allow read: if isAuthenticated() &&
                     (resource.data.customerId == request.auth.uid ||
                      resource.data.vendorId == request.auth.uid ||
                      resource.data.driverId == request.auth.uid) ||
                     isAdmin();
      // Only customers can create disputes
      allow create: if isCustomer() && isActiveUser() &&
                       request.resource.data.customerId == request.auth.uid;
      // Only admins can update disputes (resolve, etc.)
      allow update: if isAdmin();
      // Only admins can delete disputes
      allow delete: if isAdmin();
    }

    // ===== SYSTEM CONFIG COLLECTION =====
    match /systemConfig/{configId} {
      // Only admins can read system configuration
      allow read: if isAdmin();
      // Only admins can write system configuration
      allow write: if isAdmin();
    }

    // ===== SYSTEM COLLECTION =====
    match /_system/{docId} {
      // System documentation - admins can read/write, others can read
      allow read: if true; // Allow developers to read documentation
      allow write: if isAdmin();
    }

  }
}
