{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalEnv": ["FIREBASE_ADMIN_PROJECT_ID", "FIREBASE_ADMIN_PRIVATE_KEY", "FIREBASE_ADMIN_CLIENT_EMAIL", "MAPS_BACKEND_KEY", "BONSAI_HOST", "BONSAI_USERNAME", "BONSAI_PASSWORD", "CLOUDINARY_API_KEY", "CLOUDINARY_API_SECRET", "CLOUDINARY_WEBHOOK_SECRET", "PAYMONGO_PUBLIC_KEY_LIVE", "PAYMONGO_SECRET_KEY_LIVE", "PAYMONGO_PUBLIC_KEY_TEST", "PAYMONGO_SECRET_KEY_TEST", "DATABASE_URL", "DATABASE_SSL", "DATABASE_POOL_MIN", "DATABASE_POOL_MAX", "DATABASE_CONNECTION_TIMEOUT", "REDIS_URL", "REDIS_TOKEN", "RESEND_API_KEY", "RESEND_FROM_EMAIL", "OPENAI_API_KEY", "GOOGLE_GENERATIVE_AI_API_KEY", "NEXT_PUBLIC_GOOGLE_MAPS_API_KEY", "NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME", "NEXT_PUBLIC_CLOUDINARY_API_KEY", "NEXT_PUBLIC_FIREBASE_API_KEY", "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN", "NEXT_PUBLIC_FIREBASE_PROJECT_ID", "NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET", "NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID", "NEXT_PUBLIC_FIREBASE_APP_ID", "NEXT_PUBLIC_FIREBASE_VAPID_KEY", "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY", "STRIPE_SECRET_KEY", "STRIPE_WEBHOOK_SECRET", "NEXT_PUBLIC_APP_URL", "NEXT_PUBLIC_API_URL", "SUPABASE_SERVICE_ROLE_KEY", "ENABLE_SUPABASE_CMS", "UPSTASH_REDIS_REST_URL", "UPSTASH_REDIS_REST_TOKEN", "ENABLE_REDIS_CACHING", "REDIS_DEFAULT_TTL", "ENABLE_EMAIL_NOTIFICATIONS", "EMAIL_FROM_NAME", "EMAIL_REPLY_TO", "GOOGLE_AI_API_KEY", "ENABLE_AI_FEATURES", "AI_MODEL_DEFAULT", "NODE_VERSION", "NEXT_TELEMETRY_DISABLED", "NODE_ENV", "CI", "VERCEL", "VERCEL_URL", "VERCEL_ENV", "VERCEL_GIT_COMMIT_SHA", "VERCEL_GIT_COMMIT_MESSAGE", "VERCEL_GIT_COMMIT_AUTHOR_NAME", "VERCEL_GIT_PROVIDER", "VERCEL_GIT_REPO_SLUG", "VERCEL_GIT_REPO_OWNER", "VERCEL_GIT_REPO_ID", "VERCEL_GIT_COMMIT_REF"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**", "lib/**", "*.tsbuildinfo"], "env": ["NODE_VERSION", "NEXT_TELEMETRY_DISABLED", "NODE_ENV", "CI", "VERCEL", "VERCEL_URL", "VERCEL_ENV"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"], "env": ["NODE_VERSION", "NEXT_TELEMETRY_DISABLED", "NODE_ENV"]}, "type-check": {"dependsOn": ["^build"], "env": ["NODE_VERSION", "NEXT_TELEMETRY_DISABLED", "NODE_ENV"]}, "clean": {"cache": false}, "ios": {"cache": false, "persistent": true}, "android": {"cache": false, "persistent": true}}}